{"questionnaire": {"questions": {"electricity": {"question": "¿Tiene acceso confiable a electricidad?", "options": {"yes": "S<PERSON>, energía confiable", "sometimes": "A veces impredecible", "no": "Cortes frecuentes"}}, "users": {"question": "¿Cuántos usuarios compartirán esta computadora?", "options": {"1-2": "1-2 usuarios", "3-5": "3-5 usuarios", "6+": "6+ usuarios"}}, "growth": {"question": "¿Se espera un crecimiento comunitario en los próximos 2 años?", "options": {"low": "Crecimiento mínimo", "medium": "Crecimiento moderado", "high": "Crecimiento rápido"}}, "reuse": {"question": "¿Tiene una computadora disponible para reutilizar?", "options": {"yes": "Sí", "no": "No"}}, "format": {"question": "¿Puede formatear y configurar una computadora?", "options": {"yes": "Sí, con confianza", "maybe": "Con guía", "no": "No"}}, "price": {"question": "¿Cuál es su rango de presupuesto?", "options": {"low": "$0-100", "medium": "$100-300", "high": "$300+"}}, "points": {"question": "Asignar 5 puntos de importancia", "description": "Distribuya 5 puntos entre estas prioridades según su importancia para usted", "options": {"skip": "<PERSON><PERSON><PERSON><PERSON>"}, "priorities": {"easyToUse": {"title": "<PERSON><PERSON><PERSON><PERSON> de usar", "description": "Interfaz y operación sencilla"}, "lowPower": {"title": "Consume poca electricidad", "description": "Operación eficiente en energía"}, "language": {"title": "Funciona en su idioma", "description": "Interfaz y contenido en su idioma preferido"}, "scalable": {"title": "<PERSON><PERSON><PERSON> crecer si es necesario", "description": "Expandible para necesidades futuras"}, "lowCost": {"title": "Costo muy bajo", "description": "Costos iniciales y de mantenimiento asequibles"}}, "pointsRemaining": "<PERSON>untos restantes: {{count}}", "pointsRemaining_one": " Punto restante: {{count}}", "pointsRemaining_other": "<PERSON>untos restantes: {{count}}", "maxPointsReached": "Cantidad máxima de puntos alcanzada", "continue": "<PERSON><PERSON><PERSON><PERSON>"}, "mainUse": {"question": "¿Cuál es el uso principal previsto?", "options": {"education": "Educación", "business": "Nego<PERSON><PERSON>", "personal": "Personal"}}, "usage": {"question": "¿Para qué usará esto primero su comunidad?", "description": "Seleccione todas las que apliquen", "options": {"news": {"title": "Compartir noticias o información", "description": "Distribuir noticias locales y actualizaciones importantes"}, "files": {"title": "Almacenar archivos importantes", "description": "Guardar y compartir documentos de forma segura"}, "education": {"title": "Servicios educativos o escolares", "description": "Apoyar el aprendizaje y recursos educativos"}, "media": {"title": "Medios comunitarios", "description": "Películas, música, radio en línea, podcasts, etc."}, "other": {"title": "<PERSON><PERSON><PERSON>", "description": "Indíquenos para qué más lo usará", "placeholder": "Por favor especifique..."}}, "continue": "<PERSON><PERSON><PERSON><PERSON>", "selectAtLeastOne": "Por favor, seleccione al menos una opción"}, "results": {"title": "Su recomendación de Caja Comunitaria", "subtitle": "Basado en sus respuestas, recomendamos:", "matchScore": "Puntuación de compatibilidad", "perfectMatch": "¡Compatibilidad perfecta!", "excellentMatch": "Excelente compatibilidad", "goodMatch": "Buena compatibilidad", "fairMatch": "Compatibilidad regular", "viewAlternatives": "<PERSON><PERSON> Alternativas", "hideAlternatives": "<PERSON><PERSON><PERSON><PERSON>", "whyThisRecommendation": "¿Por qué esta recomendación?", "attributes": {"energy": "Eficiencia energética", "concurrency": "<PERSON><PERSON><PERSON><PERSON> usuarios", "growth": "Potencial de crecimiento", "reusable": "Reusabilidad", "formatEase": "Facilidad de configuración", "cost": "Costo-eficiencia"}, "devices": {"raspberryPi": {"name": "Raspberry Pi", "description": "Computadora de placa única de bajo consumo, asequible, ideal para necesidades básicas de la comunidad", "pros": ["Consumo muy bajo de energía", "Costo inicial asequible", "Compacta y portátil"], "cons": ["Procesamiento limitado", "No es ideal para muchos usuarios concurrentes", "Opciones de expansión limitadas"], "idealFor": "Comunidades con electricidad y presupuesto limitados, que sirven necesidades básicas de información"}, "zimaBoard": {"name": "ZimaBoard", "description": "Servidor de placa única con buen equilibrio entre eficiencia energética y rendimiento", "pros": ["Buena eficiencia energética", "Mejor rendimiento que Raspberry Pi", "Opciones de almacenamiento expandible"], "cons": ["Costo mayor que <PERSON><PERSON><PERSON>", "Soporte moderado para usuarios concurrentes", "<PERSON><PERSON>e requerir conocimientos técnicos para configurar"], "idealFor": "Comunidades pequeñas a medianas con necesidades técnicas moderadas y energía estable"}, "intelNUC": {"name": "Intel NUC", "description": "Mini PC compacto y potente con excelente rendimiento para diversas necesidades comunitarias", "pros": ["Capacidades de procesamiento poderosas", "Soporta muchos usuarios concurrentes", "Excelente para crecimiento y expansión"], "cons": ["Mayor consu<PERSON> de energía", "Inversión inicial más cara", "Requiere electricidad estable"], "idealFor": "Comunidades en crecimiento con electricidad confiable y necesidades digitales diversas"}, "reusedPC": {"name": "<PERSON>o", "description": "Computadora de escritorio reutilizada, equilibrando costo con rendimiento decente", "pros": ["Sin costo adicional de hardware", "A menudo buen rendimiento", "Componentes fácilmente actualizables"], "cons": ["Mayor consu<PERSON> de energía", "Fiabilidad variable según la edad", "Podría requerir mantenimiento"], "idealFor": "Comunidades con hardware existente y habilidades técnicas para mantenimiento"}}, "reasonings": {"energy": "Coincide con sus restricciones energéticas", "concurrency": "Soporta su necesidad de usuarios", "growth": "Se alinea con sus expectativas de crecimiento", "reusable": "Utiliza eficientemente hardware existente", "formatEase": "Se ajusta a sus capacidades técnicas de configuración", "cost": "Se ajusta a su presupuesto"}, "startOver": "<PERSON><PERSON><PERSON> de nuevo", "exportResults": "Exportar resultados", "calculating": "Calculando la mejor coincidencia...", "pros": "Ventaja<PERSON>", "cons": "Desventajas", "idealFor": "Ideal para", "generating": "Generando...", "shareMyAnswers": "Compartir mis respuestas", "exportAsPDF": "Exportar como PDF", "downloadPDF": "Descargar PDF", "sharePDF": "Compartir PDF", "shareViaWhatsApp": "Compartir vía WhatsApp", "shareViaEmail": "Compartir vía Email", "exportAsText": "Exportar como Texto", "linkCopied": "Enlace copiado al portapapeles! Cualquier que tenga este enlace puede ver estas recomendaciones.", "linkCopyFailed": "Error al copiar el enlace. Inténtelo de nuevo.", "shareTitle": "Recomendación de Caja Comunitaria", "shareText": "Revisa mi recomendación de Caja Comunitaria", "emailSubject": "Mi Recomendación de Caja Comunitaria", "emailBody": "¡Recibí una recomendación para un {{device}} para las necesidades de mi caja comunitaria!"}}, "buttons": {"next": "Siguient<PERSON>"}}, "notFound": {"title": "404", "message": "¡Ups! Página no encontrada", "returnHome": "Volver a Inicio"}, "errors": {"contextError": "useQuestionnaire debe usarse dentro de un QuestionnaireProvider"}, "language": {"select": "Idioma"}, "landing": {"title": "Buscador de Plataformas Digitales Comunitarias", "description": "Descubre la mejor configuración tecnológica para las necesidades de tu comunidad. Responde unas preguntas simples y obtiene una guía personalizada.", "startButton": "Comenzar", "altHeroImage": "Colaboración comunitaria", "footer": "Proyecto Caja Comunitaria"}}