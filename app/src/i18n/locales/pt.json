{"questionnaire": {"questions": {"electricity": {"question": "Você tem acesso confiável à eletricidade?", "options": {"yes": "<PERSON><PERSON>, energia confiável", "sometimes": "Às vezes instável", "no": "<PERSON><PERSON> frequentes"}}, "users": {"question": "Quantos usuários compartilharão este computador?", "options": {"1-2": "1-2 usuários", "3-5": "3-5 usuários", "6+": "6+ us<PERSON><PERSON><PERSON><PERSON>"}}, "growth": {"question": "Crescimento esperado da comunidade nos próximos 2 anos?", "options": {"low": "Crescimento mínimo", "medium": "Crescimento moderado", "high": "Crescimento rápido"}}, "reuse": {"question": "Você tem um computador disponível para reutilização?", "options": {"yes": "<PERSON>m", "no": "Não"}}, "format": {"question": "Você consegue formatar e configurar um computador?", "options": {"yes": "Sim, com confiança", "maybe": "Com orientação", "no": "Não"}}, "price": {"question": "Qual é a sua faixa de orçamento?", "options": {"low": "US$0-100", "medium": "US$100-300", "high": "US$300+"}}, "points": {"question": "Atribua 5 pontos de importância", "description": "Distribua 5 pontos entre essas prioridades com base na importância para você", "options": {"skip": "<PERSON><PERSON><PERSON><PERSON>"}, "priorities": {"easyToUse": {"title": "<PERSON><PERSON><PERSON><PERSON> de usar", "description": "Interface e operação simples"}, "lowPower": {"title": "Consome pouca energia", "description": "Operação eficiente em termos de energia"}, "language": {"title": "Funciona no seu idioma", "description": "Interface e conteúdo no idioma de sua preferência"}, "scalable": {"title": "Pode crescer se necessário", "description": "Expansível para necessidades futuras"}, "lowCost": {"title": "<PERSON>usto muito baixo", "description": "Custos iniciais e de manutenção acessíveis"}}, "pointsRemaining": "Pontos restantes: {{count}}", "pointsRemaining_one": "<PERSON><PERSON> restante: {{count}}", "pointsRemaining_other": "Pontos restantes: {{count}}", "maxPointsReached": "Pontuação máxima atingida", "continue": "<PERSON><PERSON><PERSON><PERSON>"}, "mainUse": {"question": "Qual o uso principal desejado?", "options": {"education": "Educação", "business": "<PERSON>eg<PERSON><PERSON><PERSON>", "personal": "Pessoal"}}, "usage": {"question": "Para que sua comunidade usará isto primeiro?", "description": "Se<PERSON><PERSON>e todas as opções que se aplicam", "options": {"news": {"title": "Compartilhar notícias ou informações", "description": "Distribuir notícias locais e atualizações importantes"}, "files": {"title": "Armazenar arquivos importantes", "description": "Salvar e compartilhar documentos de forma segura"}, "education": {"title": "Serviços escolares ou educacionais", "description": "Apoiar aprendizagem e recursos educacionais"}, "media": {"title": "Mídia comunitária", "description": "Filmes, música, rádio na web, podcasts, etc."}, "other": {"title": "Outro", "description": "Diga-nos para que mais você usará", "placeholder": "Por favor, especifique..."}}, "continue": "<PERSON><PERSON><PERSON><PERSON>", "selectAtLeastOne": "Por favor, selecione pelo menos uma opção"}, "results": {"title": "Sua Recomendação de Caixa Comunitária", "subtitle": "Com base em suas respostas, recomendamos:", "matchScore": "Pontuação de Compatibilidade", "perfectMatch": "Compatível Perfeito!", "excellentMatch": "Excelente Compatibilidade", "goodMatch": "Boa Compatibilidade", "fairMatch": "Compatibilidade Justa", "viewAlternatives": "<PERSON><PERSON> Alternativas", "hideAlternatives": "<PERSON><PERSON><PERSON><PERSON>", "whyThisRecommendation": "Por que esta recomendação?", "attributes": {"energy": "Eficiência energética", "concurrency": "V<PERSON>rios usuá<PERSON>s", "growth": "Potencial de crescimento", "reusable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formatEase": "Facilidade de configuração", "cost": "Custo-benefício"}, "devices": {"raspberryPi": {"name": "Raspberry Pi", "description": "Computador de placa única de baixo consumo, acessível, ideal para necessidades básicas da comunidade", "pros": ["Consumo de energia muito baixo", "Custo inicial acessível", "Compacto e portátil"], "cons": ["Potência de processamento limitada", "Não ideal para muitos usuários simultâneos", "Opções limitadas de expansão"], "idealFor": "Comunidades com eletricidade e orçamento limitados, atendendo às necessidades básicas de informação"}, "zimaBoard": {"name": "ZimaBoard", "description": "Servidor de placa única com bom equilíbrio entre eficiência e desempenho", "pros": ["Boa eficiência energética", "Desempenho melhor que o Raspberry Pi", "Opções de armazenamento expansível"], "cons": ["Custo maior que o Raspberry Pi", "Suporte moderado para vários usuários", "Pode requerer conhecimentos técnicos para configurar"], "idealFor": "Comunidades pequenas a médias com necessidades técnicas moderadas e energia estável"}, "intelNUC": {"name": "Intel NUC", "description": "Mini PC compacto e potente, com ótimo desempenho para diversas necessidades da comunidade", "pros": ["Capacidades de processamento poderosas", "Suporta muitos usuários simultâneos", "Excelente para crescimento e expansão"], "cons": ["<PERSON><PERSON> consu<PERSON> de energia", "Investimento inicial mais caro", "<PERSON>quer eletricidade estável"], "idealFor": "Comunidades em crescimento com eletricidade confiável e necessidades digitais diversas"}, "reusedPC": {"name": "<PERSON>o", "description": "Computador desktop reaproveitado, equilibrando economia e desempenho decente", "pros": ["Sem custo adicional de hardware", "Costuma ter bom desempenho", "Componentes facilmente atualizáveis"], "cons": ["<PERSON><PERSON> consu<PERSON> de energia", "Confiabilidade variável dependendo da idade", "Pode precisar de manutenção"], "idealFor": "Comunidades com hardware existente e habilidades técnicas para manutenção"}}, "reasonings": {"energy": "Corresponde às suas restrições de energia", "concurrency": "Atende às suas necessidades de usuários", "growth": "Alinha-se às suas expectativas de crescimento", "reusable": "Utiliza hardware existente de forma eficiente", "formatEase": "Corresponde às suas capacidades técnicas de configuração", "cost": "Encaixa no seu orçamento"}, "startOver": "<PERSON><PERSON><PERSON><PERSON>", "exportResults": "Exportar Resultados", "calculating": "Calculando a melhor correspondência...", "pros": "Prós", "cons": "Contras", "idealFor": "Ideal Para", "generating": "Gerando...", "shareMyAnswers": "Compartilhar Minhas Respostas", "exportAsPDF": "Exportar como PDF", "downloadPDF": "Baixar PDF", "sharePDF": "Compartilhar PDF", "shareViaWhatsApp": "Compartilhar via WhatsApp", "shareViaEmail": "Compartilhar via Email", "exportAsText": "Exportar como Texto", "linkCopied": "Link copiado para a área de transferência! Qualquer pessoa com este link pode ver essas recomendações.", "linkCopyFailed": "Falha ao copiar o link. Por favor, tente novamente.", "shareTitle": "Recomendação da Caixa Comunitária", "shareText": "Confira minha recomendação de Caixa Comunitária", "emailSubject": "Minha Recomendação de Caixa Comunitária", "emailBody": "Recebi uma recomendação para um {{device}} para as necessidades da minha caixa comunitária!"}}, "buttons": {"next": "Próximo"}}, "notFound": {"title": "404", "message": "Oops! Página não encontrada", "returnHome": "Voltar para a Home"}, "errors": {"contextError": "useQuestionnaire deve ser usado dentro de um QuestionnaireProvider"}, "language": {"select": "Idioma"}, "landing": {"title": "Encontrador de Plataformas Digitais Comunitárias", "description": "Descubra a melhor configuração de tecnologia para as necessidades da sua comunidade. Responda a algumas perguntas simples e obtenha um guia personalizado.", "startButton": "<PERSON><PERSON><PERSON>", "altHeroImage": "Colaboração comunitária", "footer": "Projeto Caixa Comunitária"}}