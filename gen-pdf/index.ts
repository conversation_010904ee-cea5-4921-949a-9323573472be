// pdf-generator.ts
import { readFileSync, writeFileSync, existsSync, readdirSync, statSync } from 'fs';
import { join, resolve, dirname } from 'path';
import { marked } from 'marked';
import puppeteer from 'puppeteer';
import { format } from 'date-fns';
import matter from 'gray-matter';
import { createHash } from 'crypto';

// manifest.yaml
const manifestYaml = `
title: "Community-Centered Connectivity Initiative (CCCI) Comparative Study 2024"
author: "CCCI Research Team"
version: "1.0.0"
date: "2024"
output:
  filename: "CCCI_Comparative_Study_2024.pdf"
  pageSize: "A4"
  margins:
    top: "2cm"
    bottom: "2cm"
    left: "2.5cm"
    right: "2.5cm"

# Define the order and structure of the PDF
structure:
  - title: "Executive Summary"
    file: "about.md"
    
  - title: "1. Introduction"
    sections:
      - title: "1.1 Overview"
        file: "introduction/overview.md"
      - title: "1.2 Research Methodology"
        file: "introduction/methodology.md"
        
  - title: "2. Hardware Analysis"
    sections:
      - title: "2.1 Hardware Comparison Overview"
        file: "hardware-guides/hardware-comparison-overview.md"
      - title: "2.2 Intel NUC"
        sections:
          - file: "hardware-guides/intel-nuc/nuc-models-comparison.md"
          - file: "hardware-guides/intel-nuc/nuc-overview.md"
          - file: "hardware-guides/intel-nuc/nuc-bios-setup.md"
      - title: "2.3 Raspberry Pi"
        sections:
          - file: "hardware-guides/raspberry-pi/rpi-models-comparison.md"
          - file: "hardware-guides/raspberry-pi/rpi-overview.md"
          - file: "hardware-guides/raspberry-pi/rpi-cooling-power.md"
      - title: "2.4 ZimaBoard"
        sections:
          - file: "hardware-guides/zimaboard/zima-models-comparison.md"
          - file: "hardware-guides/zimaboard/zima-overview.md"
          - file: "hardware-guides/zimaboard/zima-expansion-options.md"
      - title: "2.5 Recycled Computers"
        sections:
          - file: "hardware-guides/recycled-computer/recycled-models-comparison.md"
          - file: "hardware-guides/recycled-computer/recycled-pc-requirements.md"
          
  - title: "3. Software Platform Analysis"
    sections:
      - title: "3.1 Platform Comparison"
        file: "software-platforms/platform-comparison.md"
      - title: "3.2 YunoHost"
        file: "software-platforms/yunohost-overview.md"
      - title: "3.3 CapRover"
        file: "software-platforms/caprover-overview.md"
      - title: "3.4 CasaOS"
        file: "software-platforms/casaos-overview.md"
        
  - title: "4. Essential Services Implementation"
    sections:
      - title: "4.1 Content Distribution"
        sections:
          - file: "essential-services/content-distribution/local-media-server.md"
          - file: "essential-services/content-distribution/educational-content-repo.md"
      - title: "4.2 Communication Tools"
        sections:
          - file: "essential-services/communication-tools/chat-server-setup.md"
          - file: "essential-services/communication-tools/video-conferencing.md"
      - title: "4.3 Network Services"
        sections:
          - file: "essential-services/network-services/captive-portal-config.md"
          - file: "essential-services/network-services/bandwidth-management.md"
          
  - title: "5. Network Configuration"
    sections:
      - title: "5.1 LibreMesh Integration"
        sections:
          - file: "network-configuration/libremesh-integration/mesh-network-basics.md"
          - file: "network-configuration/libremesh-integration/captive-portal-guide.md"
      - title: "5.2 Router Setup"
        sections:
          - file: "network-configuration/router-setup/router-selection-guide.md"
          - file: "network-configuration/router-setup/libremesh-installation.md"
          
  - title: "6. System Management"
    sections:
      - file: "system-management/etcher-guide.md"
      - file: "system-management/disk-partitioning.md"
      - file: "system-management/security-best-practices.md"
      - file: "system-management/server-maintenance.md"
      
  - title: "7. Case Studies"
    sections:
      - file: "case-stories/rural-school-network.md"
      - file: "case-stories/island-community-mesh.md"
      - file: "case-stories/urban-neighborhood-portal.md"
      - file: "case-stories/refugee-camp-connectivity.md"
      
  - title: "8. Appendices"
    sections:
      - title: "A. Installation Guides"
        file: "appendices/installation-guides.md"
      - title: "B. Troubleshooting"
        file: "appendices/troubleshooting-guide.md"
      - title: "C. Community Resources"
        file: "appendices/community-resources.md"
`;

// config.ts
interface PDFConfig {
  title: string;
  author: string;
  version: string;
  date: string;
  output: {
    filename: string;
    pageSize: string;
    margins: {
      top: string;
      bottom: string;
      left: string;
      right: string;
    };
  };
  structure: Section[];
}

interface Section {
  title: string;
  file?: string;
  sections?: Section[];
}

interface ProcessedContent {
  title: string;
  content: string;
  level: number;
  pageBreak?: boolean;
}

// markdown-processor.ts
class MarkdownProcessor {
  private cache: Map<string, { hash: string; content: string }> = new Map();
  private baseDir: string;

  constructor(baseDir: string) {
    this.baseDir = baseDir;
    this.loadCache();
  }

  private loadCache() {
    const cacheFile = join(this.baseDir, '.pdf-cache.json');
    if (existsSync(cacheFile)) {
      const cacheData = JSON.parse(readFileSync(cacheFile, 'utf-8'));
      this.cache = new Map(Object.entries(cacheData));
    }
  }

  private saveCache() {
    const cacheFile = join(this.baseDir, '.pdf-cache.json');
    const cacheData = Object.fromEntries(this.cache);
    writeFileSync(cacheFile, JSON.stringify(cacheData, null, 2));
  }

  private getFileHash(filePath: string): string {
    const content = readFileSync(filePath, 'utf-8');
    return createHash('md5').update(content).digest('hex');
  }

  processMarkdown(filePath: string): { content: string; metadata: any } {
    const fullPath = join(this.baseDir, filePath);
    
    if (!existsSync(fullPath)) {
      console.warn(`Warning: File not found: ${filePath}`);
      return { content: `<p><em>Content not found: ${filePath}</em></p>`, metadata: {} };
    }

    const currentHash = this.getFileHash(fullPath);
    const cached = this.cache.get(filePath);

    if (cached && cached.hash === currentHash) {
      return { content: cached.content, metadata: {} };
    }

    const fileContent = readFileSync(fullPath, 'utf-8');
    const { content, data: metadata } = matter(fileContent);

    // Configure marked for better PDF rendering
    marked.setOptions({
      breaks: true,
      gfm: true,
      headerIds: true,
      mangle: false,
    });

    // Custom renderer for better PDF output
    const renderer = new marked.Renderer();
    
    // Handle internal links
    renderer.link = (href, title, text) => {
      if (href.startsWith('./') || href.startsWith('../')) {
        // Convert relative MD links to PDF internal links
        const linkText = title ? `title="${title}"` : '';
        return `<a href="#${this.generateAnchorId(href)}" ${linkText}>${text}</a>`;
      }
      return `<a href="${href}" target="_blank">${text}</a>`;
    };

    // Add IDs to headers for navigation
    renderer.heading = (text, level) => {
      const id = this.generateAnchorId(text);
      return `<h${level} id="${id}">${text}</h${level}>`;
    };

    // Better table rendering
    renderer.table = (header, body) => {
      return `<table class="pdf-table">
        <thead>${header}</thead>
        <tbody>${body}</tbody>
      </table>`;
    };

    // Process code blocks with syntax highlighting placeholder
    renderer.code = (code, language) => {
      return `<pre class="code-block" data-language="${language || 'text'}"><code>${this.escapeHtml(code)}</code></pre>`;
    };

    marked.use({ renderer });
    
    const processedContent = marked.parse(content);
    
    // Cache the processed content
    this.cache.set(filePath, { hash: currentHash, content: processedContent });
    this.saveCache();

    return { content: processedContent, metadata };
  }

  private generateAnchorId(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  private escapeHtml(text: string): string {
    const map: { [key: string]: string } = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#039;',
    };
    return text.replace(/[&<>"']/g, (m) => map[m]);
  }
}

// pdf-builder.ts
class PDFBuilder {
  private config: PDFConfig;
  private processor: MarkdownProcessor;
  private toc: { title: string; level: number; page?: number }[] = [];

  constructor(config: PDFConfig, processor: MarkdownProcessor) {
    this.config = config;
    this.processor = processor;
  }

  async buildPDF(outputPath: string) {
    console.log('🚀 Starting PDF generation...');
    
    const htmlContent = this.buildHTMLDocument();
    const htmlPath = join(dirname(outputPath), 'temp.html');
    writeFileSync(htmlPath, htmlContent);

    console.log('📄 Generating PDF with Puppeteer...');
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    try {
      const page = await browser.newPage();
      await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
      
      // Generate TOC with page numbers
      await page.evaluateHandle(() => {
        const tocItems = document.querySelectorAll('.toc-item');
        tocItems.forEach((item: any) => {
          const targetId = item.getAttribute('data-target');
          const target = document.getElementById(targetId);
          if (target) {
            const pageNum = Math.ceil(target.offsetTop / 1122); // A4 height in pixels
            const pageSpan = item.querySelector('.page-num');
            if (pageSpan) {
              pageSpan.textContent = pageNum.toString();
            }
          }
        });
      });

      await page.pdf({
        path: outputPath,
        format: this.config.output.pageSize as any,
        margin: this.config.output.margins,
        printBackground: true,
        displayHeaderFooter: true,
        headerTemplate: this.getHeaderTemplate(),
        footerTemplate: this.getFooterTemplate(),
      });

      console.log(`✅ PDF generated successfully: ${outputPath}`);
    } finally {
      await browser.close();
      // Clean up temp file
      if (existsSync(htmlPath)) {
        require('fs').unlinkSync(htmlPath);
      }
    }
  }

  private buildHTMLDocument(): string {
    const processedSections = this.processSections(this.config.structure, 0);
    const tocHTML = this.generateTOC();
    const contentHTML = processedSections.map(section => this.renderSection(section)).join('\n');

    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${this.config.title}</title>
  <style>
    ${this.getStyles()}
  </style>
</head>
<body>
  <div class="title-page">
    <h1>${this.config.title}</h1>
    <div class="subtitle">
      <p>Version ${this.config.version}</p>
      <p>${this.config.date}</p>
      <p>${this.config.author}</p>
    </div>
  </div>
  
  <div class="page-break"></div>
  
  <div class="toc">
    <h2>Table of Contents</h2>
    ${tocHTML}
  </div>
  
  <div class="page-break"></div>
  
  <div class="content">
    ${contentHTML}
  </div>
</body>
</html>
    `;
  }

  private processSections(sections: Section[], level: number): ProcessedContent[] {
    const processed: ProcessedContent[] = [];

    sections.forEach((section, index) => {
      if (section.file) {
        const { content } = this.processor.processMarkdown(section.file);
        processed.push({
          title: section.title,
          content,
          level,
          pageBreak: level <= 1 && index > 0,
        });
        this.toc.push({ title: section.title, level });
      } else if (section.title) {
        processed.push({
          title: section.title,
          content: '',
          level,
          pageBreak: level <= 1 && index > 0,
        });
        this.toc.push({ title: section.title, level });
      }

      if (section.sections) {
        processed.push(...this.processSections(section.sections, level + 1));
      }
    });

    return processed;
  }

  private generateTOC(): string {
    return this.toc
      .map((item) => {
        const indent = '  '.repeat(item.level);
        const id = this.generateAnchorId(item.title);
        return `
          <div class="toc-item toc-level-${item.level}" data-target="${id}">
            ${indent}<a href="#${id}">${item.title}</a>
            <span class="dots"></span>
            <span class="page-num"></span>
          </div>
        `;
      })
      .join('\n');
  }

  private renderSection(section: ProcessedContent): string {
    const id = this.generateAnchorId(section.title);
    const pageBreak = section.pageBreak ? '<div class="page-break"></div>' : '';
    const headerLevel = Math.min(section.level + 2, 6);
    
    return `
      ${pageBreak}
      <section class="content-section level-${section.level}">
        <h${headerLevel} id="${id}" class="section-title">${section.title}</h${headerLevel}>
        ${section.content}
      </section>
    `;
  }

  private generateAnchorId(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  private getStyles(): string {
    return `
      @page {
        size: A4;
        margin: ${this.config.output.margins.top} ${this.config.output.margins.right} ${this.config.output.margins.bottom} ${this.config.output.margins.left};
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
        font-size: 11pt;
        line-height: 1.6;
        color: #333;
        margin: 0;
        padding: 0;
      }

      .title-page {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        text-align: center;
      }

      .title-page h1 {
        font-size: 32pt;
        margin-bottom: 2rem;
        color: #1a1a1a;
      }

      .subtitle {
        font-size: 14pt;
        color: #666;
      }

      .subtitle p {
        margin: 0.5rem 0;
      }

      .page-break {
        page-break-after: always;
      }

      .toc {
        padding: 2rem 0;
      }

      .toc h2 {
        font-size: 24pt;
        margin-bottom: 2rem;
        color: #1a1a1a;
      }

      .toc-item {
        display: flex;
        align-items: baseline;
        margin: 0.5rem 0;
        font-size: 11pt;
      }

      .toc-level-0 {
        font-weight: bold;
        font-size: 12pt;
        margin-top: 1rem;
      }

      .toc-level-1 {
        margin-left: 2rem;
      }

      .toc-level-2 {
        margin-left: 4rem;
        font-size: 10pt;
      }

      .toc-item a {
        color: #333;
        text-decoration: none;
      }

      .toc-item .dots {
        flex: 1;
        border-bottom: 1px dotted #999;
        margin: 0 0.5rem;
        min-width: 1rem;
      }

      .toc-item .page-num {
        color: #666;
      }

      .content-section {
        margin-bottom: 2rem;
      }

      .section-title {
        color: #1a1a1a;
        margin-top: 2rem;
        margin-bottom: 1rem;
      }

      h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        color: #1a1a1a;
      }

      h1 { font-size: 24pt; }
      h2 { font-size: 20pt; }
      h3 { font-size: 16pt; }
      h4 { font-size: 14pt; }
      h5 { font-size: 12pt; }
      h6 { font-size: 11pt; }

      p {
        margin-bottom: 1rem;
        text-align: justify;
        orphans: 3;
        widows: 3;
      }

      ul, ol {
        margin-bottom: 1rem;
        padding-left: 2rem;
      }

      li {
        margin-bottom: 0.5rem;
      }

      .pdf-table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
        font-size: 10pt;
        page-break-inside: avoid;
      }

      .pdf-table th,
      .pdf-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }

      .pdf-table th {
        background-color: #f5f5f5;
        font-weight: bold;
      }

      .pdf-table tr:nth-child(even) {
        background-color: #f9f9f9;
      }

      .code-block {
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 1rem;
        font-family: 'Courier New', Courier, monospace;
        font-size: 9pt;
        overflow-x: auto;
        page-break-inside: avoid;
        margin: 1rem 0;
      }

      blockquote {
        border-left: 4px solid #ddd;
        padding-left: 1rem;
        margin: 1rem 0;
        color: #666;
        font-style: italic;
      }

      img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 1rem auto;
        page-break-inside: avoid;
      }

      a {
        color: #0066cc;
        text-decoration: none;
      }

      a:hover {
        text-decoration: underline;
      }

      .level-0 > .section-title {
        font-size: 24pt;
        margin-top: 3rem;
        border-bottom: 2px solid #333;
        padding-bottom: 0.5rem;
      }

      .level-1 > .section-title {
        font-size: 18pt;
        margin-top: 2rem;
      }

      .level-2 > .section-title {
        font-size: 14pt;
        margin-top: 1.5rem;
      }

      @media print {
        .page-break {
          page-break-after: always;
        }
      }
    `;
  }

  private getHeaderTemplate(): string {
    return `
      <div style="font-size: 10px; color: #666; width: 100%; text-align: center; margin: 0 2cm;">
        ${this.config.title}
      </div>
    `;
  }

  private getFooterTemplate(): string {
    return `
      <div style="font-size: 10px; color: #666; width: 100%; display: flex; justify-content: space-between; margin: 0 2cm;">
        <span>${this.config.version}</span>
        <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
      </div>
    `;
  }
}

// main.ts
import { parse } from 'yaml';

async function generatePDF() {
  try {
    // Load configuration
    const config: PDFConfig = parse(manifestYaml);
    
    // Initialize processor and builder
    const processor = new MarkdownProcessor('./content');
    const builder = new PDFBuilder(config, processor);
    
    // Generate PDF
    const outputPath = resolve(config.output.filename);
    await builder.buildPDF(outputPath);
    
    console.log('✨ PDF generation complete!');
    console.log(`📍 Output: ${outputPath}`);
  } catch (error) {
    console.error('❌ Error generating PDF:', error);
    process.exit(1);
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
CCCI PDF Generator

Usage:
  ts-node pdf-generator.ts [options]

Options:
  -h, --help     Show this help message
  -w, --watch    Watch for changes and regenerate PDF
  -c, --config   Path to custom manifest.yaml file
  -o, --output   Override output filename

Examples:
  ts-node pdf-generator.ts
  ts-node pdf-generator.ts --watch
  ts-node pdf-generator.ts --config custom-manifest.yaml
  ts-node pdf-generator.ts --output draft.pdf
    `);
    process.exit(0);
  }

  if (args.includes('--watch') || args.includes('-w')) {
    console.log('👀 Watching for changes...');
    // Implement file watching logic here
    const chokidar = require('chokidar');
    const watcher = chokidar.watch('./content/**/*.md', {
      persistent: true,
      ignoreInitial: true,
    });

    watcher.on('change', async (path: string) => {
      console.log(`📝 File changed: ${path}`);
      await generatePDF();
    });

    // Generate initial PDF
    generatePDF();
  } else {
    generatePDF();
  }
}

// package.json dependencies
const packageJson = {
  "name": "ccci-pdf-generator",
  "version": "1.0.0",
  "description": "PDF generator for CCCI Comparative Study",
  "main": "pdf-generator.js",
  "scripts": {
    "build": "tsc",
    "generate": "ts-node pdf-generator.ts",
    "generate:watch": "ts-node pdf-generator.ts --watch",
    "clean": "rm -rf dist *.pdf .pdf-cache.json"
  },
  "dependencies": {
    "@types/node": "^20.0.0",
    "chokidar": "^3.5.3",
    "date-fns": "^2.30.0",
    "gray-matter": "^4.0.3",
    "marked": "^9.0.0",
    "puppeteer": "^21.0.0",
    "typescript": "^5.0.0",
    "yaml": "^2.3.0"
  },
  "devDependencies": {
    "ts-node": "^10.9.0"
  }
};

// tsconfig.json
const tsConfig = {
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
};
