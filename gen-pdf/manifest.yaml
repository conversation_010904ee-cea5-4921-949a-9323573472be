title: "Community-Centered Connectivity Initiative (CCCI) Comparative Study 2024"
author: "CCCI Research Team"
version: "1.0.0"
date: "2024"
output:
  filename: "CCCI_Comparative_Study_2024.pdf"
  pageSize: "A4"
  margins:
    top: "2cm"
    bottom: "2cm"
    left: "2.5cm"
    right: "2.5cm"

# Define the order and structure of the PDF
structure:
  - title: "Executive Summary"
    file: "about.md"
    
  - title: "1. Introduction"
    sections:
      - title: "1.1 Overview"
        file: "introduction/overview.md"
      - title: "1.2 Research Methodology"
        file: "introduction/methodology.md"
        
  - title: "2. Hardware Analysis"
    sections:
      - title: "2.1 Hardware Comparison Overview"
        file: "hardware-guides/hardware-comparison-overview.md"
      - title: "2.2 Intel NUC"
        sections:
          - file: "hardware-guides/intel-nuc/nuc-models-comparison.md"
          - file: "hardware-guides/intel-nuc/nuc-overview.md"
          - file: "hardware-guides/intel-nuc/nuc-bios-setup.md"
      - title: "2.3 Raspberry Pi"
        sections:
          - file: "hardware-guides/raspberry-pi/rpi-models-comparison.md"
          - file: "hardware-guides/raspberry-pi/rpi-overview.md"
          - file: "hardware-guides/raspberry-pi/rpi-cooling-power.md"
      - title: "2.4 ZimaBoard"
        sections:
          - file: "hardware-guides/zimaboard/zima-models-comparison.md"
          - file: "hardware-guides/zimaboard/zima-overview.md"
          - file: "hardware-guides/zimaboard/zima-expansion-options.md"
      - title: "2.5 Recycled Computers"
        sections:
          - file: "hardware-guides/recycled-computer/recycled-models-comparison.md"
          - file: "hardware-guides/recycled-computer/recycled-pc-requirements.md"
          
  - title: "3. Software Platform Analysis"
    sections:
      - title: "3.1 Platform Comparison"
        file: "software-platforms/platform-comparison.md"
      - title: "3.2 YunoHost"
        file: "software-platforms/yunohost-overview.md"
      - title: "3.3 CapRover"
        file: "software-platforms/caprover-overview.md"
      - title: "3.4 CasaOS"
        file: "software-platforms/casaos-overview.md"
        
  - title: "4. Essential Services Implementation"
    sections:
      - title: "4.1 Content Distribution"
        sections:
          - file: "essential-services/content-distribution/local-media-server.md"
          - file: "essential-services/content-distribution/educational-content-repo.md"
      - title: "4.2 Communication Tools"
        sections:
          - file: "essential-services/communication-tools/chat-server-setup.md"
          - file: "essential-services/communication-tools/video-conferencing.md"
      - title: "4.3 Network Services"
        sections:
          - file: "essential-services/network-services/captive-portal-config.md"
          - file: "essential-services/network-services/bandwidth-management.md"
          
  - title: "5. Network Configuration"
    sections:
      - title: "5.1 LibreMesh Integration"
        sections:
          - file: "network-configuration/libremesh-integration/mesh-network-basics.md"
          - file: "network-configuration/libremesh-integration/captive-portal-guide.md"
      - title: "5.2 Router Setup"
        sections:
          - file: "network-configuration/router-setup/router-selection-guide.md"
          - file: "network-configuration/router-setup/libremesh-installation.md"
          
  - title: "6. System Management"
    sections:
      - file: "system-management/etcher-guide.md"
      - file: "system-management/disk-partitioning.md"
      - file: "system-management/security-best-practices.md"
      - file: "system-management/server-maintenance.md"
      
  - title: "7. Case Studies"
    sections:
      - file: "case-stories/rural-school-network.md"
      - file: "case-stories/island-community-mesh.md"
      - file: "case-stories/urban-neighborhood-portal.md"
      - file: "case-stories/refugee-camp-connectivity.md"
      
  - title: "8. Appendices"
    sections:
      - title: "A. Installation Guides"
        file: "appendices/installation-guides.md"
      - title: "B. Troubleshooting"
        file: "appendices/troubleshooting-guide.md"
      - title: "C. Community Resources"
        file: "appendices/community-resources.md"