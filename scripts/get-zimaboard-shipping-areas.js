#!/usr/bin/env node

const axios = require('axios');
const cheerio = require('cheerio');

const DEFAULT_URL = 'https://www.zimaspace.com/support/shipping-policy';
const DEFAULT_USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';

async function getShippingCountries(url = DEFAULT_URL, options = {}) {
  const { verbose = false } = options;

  try {
    if (verbose) console.log(`Fetching: ${url}`);

    const response = await axios.get(url, {
      headers: { 'User-Agent': DEFAULT_USER_AGENT }
    });

    const $ = cheerio.load(response.data);
    const countries = new Set();

    // Extract text from paragraphs and list items that likely contain countries
    $('p, li, td').each((_, element) => {
      const text = $(element).text().trim();

      // Look for comma-separated lists that contain country-like words
      if (text.includes(',') && text.length > 20) {
        const potentialCountries = text
          .split(',')
          .map(item => item.trim())
          .filter(item => {
            // Basic heuristics for country names
            return item.length > 2 &&
                   item.length < 50 &&
                   /^[A-Za-z\s\-'\.]+$/.test(item) &&
                   !item.toLowerCase().includes('shipping') &&
                   !item.toLowerCase().includes('delivery');
          });

        // If we found multiple potential countries, add them
        if (potentialCountries.length >= 3) {
          potentialCountries.forEach(country => countries.add(country));
        }
      }
    });

    const result = Array.from(countries).sort();

    if (verbose) {
      console.log(`Found ${result.length} countries`);
      result.forEach((country, i) => console.log(`${i + 1}. ${country}`));
    }

    return result;

  } catch (error) {
    if (verbose) {
      console.error('Error:', error.message);
      if (error.response) {
        console.error(`HTTP ${error.response.status}: ${error.response.statusText}`);
      }
    }
    throw error;
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);

  // Parse arguments
  const urlArg = args.find(arg => arg.startsWith('--url='));
  const url = urlArg ? urlArg.split('=')[1] : DEFAULT_URL;
  const verbose = args.includes('--verbose') || args.includes('-v');
  const jsonOutput = args.includes('--json');
  const saveFile = args.find(arg => arg.startsWith('--save='));

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: node get-zimaboard-shipping-areas.js [options]

Options:
  --url=URL          URL to scrape (default: ${DEFAULT_URL})
  --verbose, -v      Show detailed output
  --json             Output as JSON only
  --save=FILE        Save results to file
  --help, -h         Show this help

Examples:
  node get-zimaboard-shipping-areas.js
  node get-zimaboard-shipping-areas.js --verbose --json
  node get-zimaboard-shipping-areas.js --url=https://example.com --save=countries.json
`);
    process.exit(0);
  }

  getShippingCountries(url, { verbose })
    .then(countries => {
      if (jsonOutput) {
        console.log(JSON.stringify(countries, null, 2));
      } else if (!verbose) {
        console.log(`Found ${countries.length} countries:`);
        console.log(JSON.stringify(countries, null, 2));
      }

      if (saveFile) {
        const fs = require('fs');
        const filename = saveFile.split('=')[1];
        const data = {
          timestamp: new Date().toISOString(),
          url,
          count: countries.length,
          countries
        };
        fs.writeFileSync(filename, JSON.stringify(data, null, 2));
        if (!jsonOutput) console.log(`Saved to ${filename}`);
      }
    })
    .catch(error => {
      console.error('Error:', error.message);
      process.exit(1);
    });
}

module.exports = { getShippingCountries };